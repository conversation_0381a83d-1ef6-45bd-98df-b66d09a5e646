'use client'
import React from 'react'
import { Col, Row } from 'antd'
import view from '@/assets/images/view/view.png'
import mindMap from '@/assets/images/view/mindMap.png'
import Video from '@/app/home/<USER>/video'
import Detail from '@/app/home/<USER>/Detail'
// import { useRouter } from 'next/navigation'

// 示例数据
const course = {
  title: '课程名称',
  tags: ['重要', '技能类', '变电运检', '工作票填写', '技能类', '变电运检'],
  summary: '本次视频主要讲解了公司扩展和功能扩展，包括...',
  videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
  chapters: [
    {
      time: '00:07',
      text: '好，我们上课。今年这个教室稍微有点小...',
      tags: ['标签1', '标签2', '标签3', '标签4', '标签5'],
    },
    {
      time: '00:18',
      text: '好，我们上课。今年这个教室稍微有点小...',
      tags: ['标签1', '标签2', '标签3', '标签4', '标签5'],
    },
    {
      time: '01:08',
      text: '好，我们上课。今年这个教室稍微有点小...',
      tags: ['标签1', '标签2', '标签3', '标签4', '标签5'],
    },
  ],
  thumbnails: [
    {
      img: view.src,
      title: '纪念十年的教学历程',
      time: '00:07',
    },
    {
      img: view.src,
      title: '回忆与学生的情感纽带和教学...',
      time: '00:07',
    },
    {
      img: view.src,
      title: '回忆与学生的情感纽带和教学...',
      time: '00:07',
    },
    {
      img: view.src,
      title: '回忆与学生的情感纽带和教学...',
      time: '00:07',
    },
    {
      img: view.src,
      title: '回忆与学生的情感纽带和教学...',
      time: '00:07',
    },
  ],
  mindMap: mindMap.src,
}
export default function CourseDetailPage() {
  // const router = useRouter()
  return (
    <div style={{ padding: 24, width: '100%', height: '100%', background: '#F3F8FF' }}>
      <Row gutter={32} className='flex gap-[16px]'>
        {/* 左侧：视频+缩略图 */}
        <Col span={13} className='rounded-[11px] bg-[#FFFFFF] p-[16px]'>
            <Video course={course} />
        </Col>
        {/* 右侧：课程信息 */}
        <Col span={10} className='rounded-[11px] bg-[#FFFFFF] p-[16px]'>
        {/* 返回 */}
          {/* <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              marginBottom: 8,
            }}
          >
            <Button type="default" onClick={() => router.back()}>
              返回首页
            </Button>
          </div> */}
            <Detail course={course} />
        </Col>
      </Row>
    </div>
  )
}
