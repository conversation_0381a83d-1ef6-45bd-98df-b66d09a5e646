import example from '@/assets/images/search/example.png'
import { useRouter } from 'next/navigation'
export default function HistoryCard({ doc }: { doc: any }) {
  const router = useRouter()
  return (
    <>
      <div
        className={
          'flex cursor-pointer flex-col items-center rounded-[8px] border-[1px] border-[#E9EAF2] hover:shadow-md'
        }
        onClick={() => {
          router.push(`/home/<USER>
        }}
      >
        <div key={doc.id} className="h-[230px] w-[364px] rounded-[8px]">
          <div className="flex h-[230px] w-full flex-col rounded-[8px]">
            <div className="h-[135px] w-full rounded-[8px]">
              <img
                src={example.src}
                alt="example"
                className="h-full w-full rounded-tl-[8px] rounded-tr-[8px]"
              />
            </div>
            <div className="flex h-[95px] w-full flex-col gap-[2px] rounded-bl-[8px] rounded-br-[8px] bg-[#FFFFFF] px-[10px] py-[10px]">
              {/* 介绍 */}
              <div className="flex gap-[10px] truncate text-[14px] font-[500] text-[#191919]">
                <span>{doc.title}</span>
                <span>{doc.time}</span>
              </div>
              <div className="flex gap-[5px] overflow-hidden text-[14px] font-[500] text-[#4E7DFE]">
                <div className="flex gap-[5px] truncate">
                  {doc.tags.map((tag: string, index: number) => (
                    <div key={index} className="whitespace-nowrap rounded-[3px] bg-[#EBF0FF] px-[16px] py-[4px]">
                      {tag}
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex gap-[10px] text-[12px] font-[400] leading-[20px] text-[#9192A9]">
                <div>更新日期</div>
                <div>{doc.updateTime}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
