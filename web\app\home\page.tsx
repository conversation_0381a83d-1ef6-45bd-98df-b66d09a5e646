'use client'
import Title from '@/app/home/<USER>/Title'
import Search from '@/app/home/<USER>/Search'
import FeatureCard from '@/app/home/<USER>/FeatureCard'
import HistoryCard from '@/app/home/<USER>/HistoryCard'
import { useState } from 'react'
import { useHomeContext } from './layout'
// mock 数据
const quickApps = [
  {
    key: 'qa',
    label: '知识问答',
    color: 'linear-gradient(214deg, #D3CEFF 0%, #F0ECFE 26%, #F8F8FD 100%)',
  },
  {
    key: 'file',
    label: '文件检测',
    color: 'linear-gradient( 219deg, #C2EFF5 0%, #E6F8FD 23%, #F4FBFD 100%)',
  },
  {
    key: 'doc',
    label: '公文写作',
    color: 'linear-gradient( 219deg, #CEDCFF 0%, #E9EDFD 23%, #F8F8FC 100%)',
  },
  {
    key: 'excel',
    label: 'excel助手',
    color: 'linear-gradient( 214deg, #D3CEFF 0%, #F0ECFE 26%, #F8F8FD 100%)',
  },
]
const historyDocs = Array.from({ length: 8 }).map((_, i) => ({
  title: '软件系统名称部署维护手册',
  id: i,
}))
export default function HomePage() {
  const { isLogin, showLoginDialog } = useHomeContext()
  const [selectedFeatureKey, setSelectedFeatureKey] = useState<string | undefined>('qa')
  const [selectedHistoryId, setSelectedHistoryId] = useState<number | undefined>(0)

  return (
    <>
      <div className="flex flex-col items-center py-[50px]">
        {!isLogin && (
          <button
            className="fixed right-[20px] top-[20px] rounded-[8px] bg-[#191919] px-[20px] py-[8px] text-[14px] font-[500] leading-[20px] text-[#FFFFFF]"
            onClick={showLoginDialog}
          >
            登录
          </button>
        )}
        {/* 顶部标题 */}
        <Title />
        {/* 输入框+按钮 */}
        <Search />
        {/* 应用快捷入口 */}
        <div className="mb-10 flex gap-4">
          {quickApps.map(app => (
            <FeatureCard
              app={app}
              key={app.key}
              selectedKey={selectedFeatureKey}
              onSelect={setSelectedFeatureKey}
            />
          ))}
        </div>
        {/* 历史文档列表 */}
        <div className="w-full max-w-5xl">
          <div className="mb-4 text-center text-[16px] font-[400] leading-[22px] text-[#9A9FAC]">
            历史文档
          </div>
          <div className="grid grid-cols-3 gap-6">
            {historyDocs.map(doc => (
              <HistoryCard
                doc={doc}
                key={doc.id}
                selectedId={selectedHistoryId}
                onSelect={setSelectedHistoryId}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  )
}
